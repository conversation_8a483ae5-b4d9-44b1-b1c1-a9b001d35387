# Exemplo de Relatório de Falhas ETL

## Cenário 1: Execução com Problemas (tb_propostas com erros)

```
===== RELATÓRIO DE FALHAS ETL =====
📅 2025-07-31 14:30:00 | ⏱️ Duração: 45min | 📊 Status: 🔴 FALHA

🔴 PROBLEMAS CRÍTICOS (2)
├─ CARREGAMENTO | tb_propostas | 6 lotes falharam - Principais erros: Campo excede tamanho máximo
│  └─ Ação: Revisar validação de dados e mapeamento de campos
└─ TRANSFORMAÇÃO | tb_leads | 15% perda de registros na transformação (18,432 vs 21,626)
   └─ Ação: Revisar lógica de joins e filtros na transformação

🟡 ALERTAS (3)  
├─ EXTRAÇÃO | newcon_clients | Volume 12% menor que baseline (65,234 vs 73,000)
│  └─ Ação: Investigar mudanças nas consultas ou dados de origem
├─ CARREGAMENTO | tb_clientes | 5 registros rejeitados no carregamento
│  └─ Ação: Revisar validações de dados e campos obrigatórios
└─ VALIDAÇÃO | tb_produtos | Performance degradada: 8.5min (baseline: 5min)
   └─ Ação: Verificar performance das consultas e conexões de rede

📊 RESUMO EXECUTIVO
├─ tb_produtos: ✅ 20,156 registros | 0 falhas
├─ tb_clientes: 🟡 72,891 registros | 1 problemas  
├─ tb_leads: 🟡 18,432 registros | 1 problemas
└─ tb_propostas: 🔴 0 registros | Falha total no carregamento

🎯 PRÓXIMOS PASSOS
1. [URGENTE] Revisar validação de dados e mapeamento de campos
2. [URGENTE] Revisar lógica de joins e filtros na transformação
3. [MÉDIO] Investigar mudanças nas consultas ou dados de origem
4. [MÉDIO] Revisar validações de dados e campos obrigatórios
5. [MÉDIO] Verificar performance das consultas e conexões de rede
```

## Cenário 2: Execução Sem Problemas

```
===== RELATÓRIO DE EXECUÇÃO ETL =====
📅 2025-07-31 14:30:00 | ⏱️ Duração: 32min | 📊 Status: ✅ SUCESSO

📊 RESUMO EXECUTIVO
├─ tb_produtos: ✅ 20,156 registros | 11 lotes
├─ tb_clientes: ✅ 73,245 registros | 37 lotes
├─ tb_leads: ✅ 19,832 registros | 10 lotes
└─ tb_propostas: ✅ 533,891 registros | 267 lotes

🎯 RESULTADO: Pipeline executado com sucesso - 647,124 registros processados
```

## Cenário 3: Execução com Alertas Menores

```
===== RELATÓRIO DE FALHAS ETL =====
📅 2025-07-31 14:30:00 | ⏱️ Duração: 38min | 📊 Status: 🟡 PARCIAL

🟡 ALERTAS (2)
├─ TRANSFORMAÇÃO | tb_leads | Campo obrigatório 'email' com 15 valores nulos
│  └─ Ação: Implementar lógica para preencher campo 'email' ou filtrar registros
└─ CARREGAMENTO | tb_clientes | 3 registros rejeitados no carregamento
   └─ Ação: Revisar validações de dados e campos obrigatórios

📊 RESUMO EXECUTIVO
├─ tb_produtos: ✅ 20,156 registros | 0 falhas
├─ tb_clientes: 🟡 73,242 registros | 1 problemas
├─ tb_leads: 🟡 19,817 registros | 1 problemas  
└─ tb_propostas: ✅ 533,891 registros | 0 falhas

🎯 PRÓXIMOS PASSOS
1. [MÉDIO] Implementar lógica para preencher campo 'email' ou filtrar registros
2. [MÉDIO] Revisar validações de dados e campos obrigatórios
```

## Como Usar o Relatório

1. **Copy/Paste Direto**: O relatório aparece nos logs do Airflow formatado para copy/paste direto
2. **Classificação Automática**: 
   - 🔴 CRÍTICO: Impedem execução completa
   - 🟡 ALERTA: Problemas que precisam atenção
   - 🔵 INFO: Informações para monitoramento
3. **Ações Sugeridas**: Cada problema vem com sugestão de correção
4. **Métricas Contextuais**: Volume, tempo, comparação com baseline
5. **Priorização**: Problemas ordenados por urgência

## Integração com Chamados

O relatório já vem formatado para ser copiado diretamente na descrição de chamados técnicos, contendo:
- Contexto claro do problema
- Dados específicos (tabelas, volumes, erros)
- Ações recomendadas priorizadas
- Baseline para comparação histórica