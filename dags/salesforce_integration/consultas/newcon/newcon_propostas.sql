 -- PROPOSTAS
        select
            'Proposta' end_point
            ,concc030.id_documento IdProposta
            ,corcc023.cd_inscricao_nacional CNPJCPF
            ,corcc030.e_mail Email
            ,LEFT(corcc023.nm_pessoa, CHARINDEX(' ', corcc023.nm_pessoa + ' ') - 1) as PrimeiroNome
            ,corcc023.nm_pessoa as NomeCompleto
            ,concat('(55', isnull(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') Celular
            ,cast(concc030.DH_Inclusao as date) dt_proposta
            ,DH_Alteracao dt_ultimaModificacao
            ,concc030.dt_adesao as dt_fechamento
            ,cast(pagto_parc_1.dt_pagto_parcela_1 as date) dt_pagamento
            ,case
                when day(concc030.dt_adesao) < 12 then convert(varchar(7), concc030.dt_adesao, 120) + '-12'
                when day(concc030.dt_adesao) = 12 then concc030.dt_adesao
                else convert(varchar(7),dateadd(month, 1, concc030.dt_adesao), 120) + '-12'
            end dt_validade
            ,case
                when concc030.st_situacao = 'C' then 'Cancelada'
                when concc030.st_situacao = 'N' then 'Ativa'
            end Status_proposta
            ,cast(concc030.vl_bem as numeric(18,2)) Valor
            ,concat(concc030.id_ponto_venda, '-', concc030.id_grupo_venda, '-', concc030.id_plano_venda, '-', concc030.id_bem) id_produto
            ,case
                when seguro.id_plano_seguro is not null then 'Sim'
                else 'Não'
            end as seguro
            ,case
    when conve002.id_cota is not null then 'Assinado'
    else 'Não assinado'
end status_contrato
            ,cast(conve002d.dt_venda as date) dt_venda
            ,concc030.id_ponto_venda
            ,conve001.CD_Ponto_Venda
            ,conve001.nm_fantasia ponto_venda
            ,conve014.nm_fantasia vendedor
        from
            concc030
            inner join
                corcc023 on
                corcc023.id_pessoa = concc030.id_pessoa
            left join
                concc036 on
                concc036.id_empresa = concc030.id_empresa
                and concc036.id_tipo_documento = concc030.id_tipo_documento
                and concc036.id_documento = concc030.id_documento
            left join
                conve002 on
                conve002.id_cota = concc036.id_cota
            left join
                conve002d on
                conve002d.id_cota = conve002.id_cota
            inner join
                    corcc026 on
                    corcc026.id_pessoa = concc030.id_pessoa
                    and corcc026.id_endereco = concc030.id_endereco
            inner join
                corcc027 on
                corcc027.id_pessoa = concc030.id_pessoa
                and corcc027.id_telefone = concc030.id_telefone
            inner join
                corcc015 ddd_endereco on
                ddd_endereco.id_cidade = corcc026.id_cidade
            inner join
                corcc015 ddd_telefone on
                ddd_telefone.id_cidade = corcc027.id_cidade
            left join
                (
                    select
                        id_cota
                        ,min(confi005.dt_pagamento) dt_pagto_parcela_1
                    from
                        confi005c
                        inner join
                            confi005 on
                            confi005.id_movimento_grupo = confi005c.id_movimento_grupo
                    where
                        confi005.id_cd_movto_fin = 10 and confi005.id_movimento_estorno = 0
                    group by id_cota
                ) pagto_parc_1 on
                pagto_parc_1.id_cota = conve002.id_cota
            left join
                corcc030 on
                corcc030.id_pessoa = concc030.id_pessoa
                and corcc030.id_e_mail = concc030.id_e_mail
            outer apply dbo.fn_dsvepeseguro(conve002.id_cota, getdate(), 10, 1) as seguro
            inner join
                conve001 on
                conve001.id_ponto_venda = concc030.id_ponto_venda
            inner join
                conve014 on
                conve014.id_comissionado = concc030.id_comissionado

inner join 
    vw_GrProduto on 
    vw_GrProduto.id_grupo = concc030.id_grupo_venda
    and vw_GrProduto.ID_Plano_Venda = concc030.ID_Plano_Venda
    and vw_GrProduto.ID_Taxa_Plano = concc030.ID_Taxa_Plano


Where 
cast(concc030.dt_adesao as date) >= CASE WHEN DAY(GETDATE()) >= 13 
    THEN DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), 13)
    ELSE DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()) - 1, 13)
END