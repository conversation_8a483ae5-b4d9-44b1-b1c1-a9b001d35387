
      with propostas_nao_alocadas as (
            select
                'Leads' end_point
                ,concc030.id_documento
                ,corcc023.cd_inscricao_nacional CNPJCPF
                ,corcc030.e_mail Email
                ,cast(concc030.dt_cadastro as date) Dt_cadastro
                ,concat('(55', isnull(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') <PERSON><PERSON><PERSON>
                ,'br' <PERSON><PERSON>
                ,left(corcc023.nm_pessoa, charindex(' ', corcc023.nm_pessoa + ' ') - 1) PrimeiroNome
                ,corcc023.nm_pessoa NomeCompleto
                ,ddd_endereco.nm_cidade Cidade
                ,ddd_endereco.id_uf Estado
                ,'Bamaq Consórcio' TipoEmpresa
                ,conbe003.nm_produto as TipoBem
                ,'Consórcio' TipoSimulacao
                ,cast(concc030.vl_bem as numeric(18,2)) Valor<PERSON><PERSON><PERSON><PERSON><PERSON>
                ,null Campaign
                ,null Source
                ,null Medium
                ,null Term
                ,null Content
                ,conve001.nm_fantasia PontoVenda
                ,conve041.nm_plano_venda PlanoVenda
                ,cast(corcc023a.dt_nascimento as date) dt_nascimento
                ,null Optin_seguros_email
                ,null Optin_seguros_SMS
                ,null Optin_seguros_wpp
                ,null Optin_capital_email
                ,null Optin_capital_SMS
                ,null Optin_capital_whatsapp
                ,cast(concc030.dh_inclusao as date) Dt_simulacao
                ,conve014.nm_fantasia NomeVendedor
            from
                concc030
                inner join
                    corcc023 on
                    corcc023.id_pessoa = concc030.id_pessoa
                left join
                    corcc023a on
                    corcc023a.id_pessoa = corcc023.id_pessoa
                left join
                    concc036 on
                    concc036.id_empresa = concc030.id_empresa
                    and concc036.id_tipo_documento = concc030.id_tipo_documento
                    and concc036.id_documento = concc030.id_documento
                left join
                    corcc030 on
                    corcc030.id_pessoa = concc030.id_pessoa
                    and corcc030.id_e_mail = concc030.id_e_mail
                inner join
                    corcc026 on
                    corcc026.id_pessoa = concc030.id_pessoa
                    and corcc026.id_endereco = concc030.id_endereco
                inner join
                    corcc027 on
                    corcc027.id_pessoa = concc030.id_pessoa
                    and corcc027.id_telefone = concc030.id_telefone
                inner join
                    corcc015 ddd_endereco on
                    ddd_endereco.id_cidade = corcc026.id_cidade
                inner join
                    corcc015 ddd_telefone on
                    ddd_telefone.id_cidade = corcc027.id_cidade
                inner join
                    conbe007 on
                    conbe007.id_bem = concc030.id_bem
                inner join
                    conve001 on
                    conve001.id_ponto_venda = concc030.id_ponto_venda
                inner join
                    conve041 on
                    conve041.id_plano_venda = concc030.id_plano_venda
                inner join
                    conve014 on
                    conve014.id_comissionado = concc030.id_comissionado
    left join  
     conbe023 (NOLOCK) ON 
     conbe007.ID_CONBE023 = conbe023.ID_CONBE023
    left join 
    conbe003 (NOLOCK) ON 
    conbe023.ID_Produto = conbe003.ID_Produto
            where
                concc036.id_cota is null
    and datediff (dd, cast(concc030.dt_cadastro as date) , cast(getdate() as date)) <=30
        ),

        vendas as (
            select
                'Leads' end_point
                ,concc036.id_documento
                ,corcc023.cd_inscricao_nacional CNPJCPF
                ,corcc030.e_mail Email
                ,conve002d.dt_cadastro Dt_cadastro
                ,concat('(55', isnull(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') Celular
                ,'br' Locale
                ,left(corcc023.nm_pessoa, charindex(' ', corcc023.nm_pessoa + ' ') - 1) PrimeiroNome
                ,corcc023.nm_pessoa NomeCompleto
                ,ddd_endereco.nm_cidade Cidade
                ,ddd_endereco.id_uf Estado
                ,'Bamaq Consórcio' TipoEmpresa
    ,conbe003.nm_produto as TipoBem
                ,'Consórcio' TipoSimulacao
                ,conve002d.vl_credito ValorSimulacao
                ,null Campaign
                ,null Source
                ,null Medium
                ,null Term
                ,null Content
                ,conve001.nm_fantasia PontoVenda
                ,conve041.nm_plano_venda PlanoVenda
                ,corcc023a.dt_nascimento
                ,null Optin_seguros_email
                ,null Optin_seguros_SMS
                ,null Optin_seguros_wpp
                ,null Optin_capital_email
                ,null Optin_capital_SMS
                ,null Optin_capital_whatsapp
                ,concc030.dh_inclusao Dt_simulacao
                ,conve014.nm_fantasia NomeVendedor
            from
                conve002
                inner join
                    conve002d on
                    conve002d.id_cota = conve002.id_cota
                left join
                    concc036 on
                    concc036.id_cota = conve002.id_cota
                left join
                    concc030 on
                    concc030.id_empresa = concc036.id_empresa
                    and concc030.id_tipo_documento = concc036.id_tipo_documento
                    and concc030.id_documento = concc036.id_documento
                inner join
                    corcc023 on
                    corcc023.id_pessoa = conve002.id_pessoa
                left join
                    corcc023a on
                    corcc023a.id_pessoa = corcc023.id_pessoa
                inner join
                    corcc030 on
                    corcc030.id_pessoa = conve002.id_pessoa
                    and corcc030.id_e_mail = conve002.id_e_mail
                inner join
                    corcc026 on
                    corcc026.id_pessoa = conve002.id_pessoa
                    and corcc026.id_endereco = conve002.id_endereco
                inner join
                    corcc027 on
                    corcc027.id_pessoa = conve002.id_pessoa
                    and corcc027.id_telefone = conve002.id_telefone
                inner join
                    corcc015 ddd_endereco on
                    ddd_endereco.id_cidade = corcc026.id_cidade
                inner join
                    corcc015 ddd_telefone on
                    ddd_telefone.id_cidade = corcc027.id_cidade
                inner join
                    conbe007 on
                    conbe007.id_bem = conve002d.id_bem
                inner join
                    conve001 on
                    conve001.id_ponto_venda = conve002.id_ponto_venda
                inner join
                    conve041 on
                    conve041.id_plano_venda = conve002.id_plano_venda
                inner join
                    conve014 on
                    conve014.id_comissionado = conve002.id_comissionado
    left join  
     conbe023 (NOLOCK) ON 
     conbe007.ID_CONBE023 = conbe023.ID_CONBE023
    left join 
    conbe003 (NOLOCK) ON 
    conbe023.ID_Produto = conbe003.ID_Produto
   where datediff (dd, cast(conve002d.dt_cadastro as date) , cast(getdate() as date)) <=30
        ),

        final as(
            select * from propostas_nao_alocadas
            union all
            select * from vendas
        )
        select * from final
    