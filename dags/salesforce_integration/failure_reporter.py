"""
ETL Consolidado - Relatório de Falhas
Sistema centralizado para coleta e relatório de falhas durante execução do ETL.
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from salesforce_integration.utils import setup_logging

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = setup_logging()

# =============================================================================
# ENUMS E CLASSES DE DADOS  
# =============================================================================

class FailurePhase(Enum):
    """Fases do ETL onde podem ocorrer falhas"""
    EXTRACTION = "EXTRAÇÃO"
    TRANSFORMATION = "TRANSFORMAÇÃO" 
    LOADING = "CARREGAMENTO"
    VALIDATION = "VALIDAÇÃO"

class FailureSeverity(Enum):
    """Níveis de severidade das falhas"""
    CRITICAL = ("🔴", "CRÍTICO")
    ALERT = ("🟡", "ALERTA") 
    INFO = ("🔵", "INFO")
    
    def __init__(self, emoji, label):
        self.emoji = emoji
        self.label = label

@dataclass
class FailureRecord:
    """Registro individual de falha"""
    phase: FailurePhase
    severity: FailureSeverity
    table_name: str
    description: str
    context: Dict[str, Any]
    timestamp: datetime
    suggested_action: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class ExecutionMetrics:
    """Métricas de execução para comparação"""
    table_name: str
    expected_records: Optional[int] = None
    actual_records: int = 0 
    expected_duration_min: Optional[int] = None
    actual_duration_min: float = 0
    baseline_records: Optional[int] = None
    processing_errors: int = 0
    rejection_count: int = 0

# =============================================================================
# CLASSE PRINCIPAL DE RELATÓRIO
# =============================================================================

class FailureReporter:
    """Sistema centralizado de coleta e relatório de falhas ETL"""
    
    def __init__(self):
        self.failures: List[FailureRecord] = []
        self.metrics: Dict[str, ExecutionMetrics] = {}
        self.start_time = datetime.now()
        self.end_time: Optional[datetime] = None
        self.logger = logging.getLogger(__name__)
        
        # Baselines esperados (podem vir de config no futuro)
        self.baselines = {
            'tb_produtos': {'records': 20000, 'duration_min': 5},
            'tb_clientes': {'records': 73000, 'duration_min': 8}, 
            'tb_leads': {'records': 15000, 'duration_min': 6},
            'tb_propostas': {'records': 533000, 'duration_min': 20}
        }
    
    def add_failure(self, 
                   phase: FailurePhase,
                   severity: FailureSeverity, 
                   table_name: str,
                   description: str,
                   context: Dict[str, Any] = None,
                   suggested_action: str = None):
        """Adiciona um registro de falha"""
        
        failure = FailureRecord(
            phase=phase,
            severity=severity,
            table_name=table_name,
            description=description,
            context=context or {},
            timestamp=datetime.now(),
            suggested_action=suggested_action
        )
        
        self.failures.append(failure)
        self.logger.warning(f"Falha registrada: {phase.value} | {table_name} | {description}")
    
    def set_table_metrics(self, table_name: str, metrics: ExecutionMetrics):
        """Define métricas de execução para uma tabela"""
        self.metrics[table_name] = metrics
        
        # Auto-detecta problemas baseado em baselines
        self._auto_detect_issues(table_name, metrics)
    
    def _auto_detect_issues(self, table_name: str, metrics: ExecutionMetrics):
        """Detecta problemas automaticamente baseado em métricas"""
        baseline = self.baselines.get(table_name, {})
        
        # Verifica volume de registros
        if baseline.get('records') and metrics.actual_records > 0:
            expected = baseline['records']
            actual = metrics.actual_records
            variance = abs(actual - expected) / expected
            
            if variance > 0.2:  # Mais de 20% de diferença
                severity = FailureSeverity.CRITICAL if variance > 0.5 else FailureSeverity.ALERT
                direction = "menor" if actual < expected else "maior"
                self.add_failure(
                    phase=FailurePhase.VALIDATION,
                    severity=severity,
                    table_name=table_name,
                    description=f"Volume {variance:.0%} {direction} que baseline ({actual:,} vs {expected:,})",
                    context={'expected': expected, 'actual': actual, 'variance': variance},
                    suggested_action="Investigar mudanças nas consultas ou dados de origem"
                )
        
        # Verifica tempo de execução
        if baseline.get('duration_min') and metrics.actual_duration_min > 0:
            expected_duration = baseline['duration_min']
            actual_duration = metrics.actual_duration_min
            
            if actual_duration > expected_duration * 1.5:  # 50% mais lento
                self.add_failure(
                    phase=FailurePhase.VALIDATION,
                    severity=FailureSeverity.ALERT,
                    table_name=table_name,
                    description=f"Performance degradada: {actual_duration:.1f}min (baseline: {expected_duration}min)",
                    context={'expected_min': expected_duration, 'actual_min': actual_duration},
                    suggested_action="Verificar performance das consultas e conexões de rede"
                )
        
        # Verifica rejeições 
        if metrics.rejection_count > 0:
            severity = FailureSeverity.CRITICAL if metrics.rejection_count > 100 else FailureSeverity.ALERT
            self.add_failure(
                phase=FailurePhase.LOADING,
                severity=severity,
                table_name=table_name,
                description=f"{metrics.rejection_count} registros rejeitados no carregamento",
                context={'rejection_count': metrics.rejection_count, 'total_records': metrics.actual_records},
                suggested_action="Revisar validações de dados e campos obrigatórios"
            )
    
    def finalize_execution(self):
        """Finaliza coleta e prepara relatório"""
        self.end_time = datetime.now()
    
    def generate_report(self) -> str:
        """Gera relatório formatado para copy/paste"""
        if not self.end_time:
            self.finalize_execution()
        
        duration = self.end_time - self.start_time
        duration_str = f"{duration.total_seconds()/60:.0f}min"
        
        # Determina status geral
        critical_count = len([f for f in self.failures if f.severity == FailureSeverity.CRITICAL])
        status = "🔴 FALHA" if critical_count > 0 else ("🟡 PARCIAL" if self.failures else "✅ SUCESSO")
        
        report = f"""
===== RELATÓRIO DE FALHAS ETL =====
📅 {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} | ⏱️ Duração: {duration_str} | 📊 Status: {status}
"""
        
        # Agrupa falhas por severidade
        by_severity = {
            FailureSeverity.CRITICAL: [f for f in self.failures if f.severity == FailureSeverity.CRITICAL],
            FailureSeverity.ALERT: [f for f in self.failures if f.severity == FailureSeverity.ALERT],
            FailureSeverity.INFO: [f for f in self.failures if f.severity == FailureSeverity.INFO]
        }
        
        # Seção de problemas por severidade
        for severity, failures in by_severity.items():
            if not failures:
                continue
                
            report += f"\n{severity.emoji} {severity.label} ({len(failures)})\n"
            
            for i, failure in enumerate(failures):
                connector = "├─" if i < len(failures) - 1 else "└─"
                report += f"{connector} {failure.phase.value} | {failure.table_name} | {failure.description}\n"
                
                # Adiciona ação sugerida se houver
                if failure.suggested_action:
                    indent = "│  " if i < len(failures) - 1 else "   "
                    report += f"{indent}└─ Ação: {failure.suggested_action}\n"
        
        # Resumo executivo por tabela
        report += "\n📊 RESUMO EXECUTIVO\n"
        for table_name, metrics in self.metrics.items():
            table_failures = [f for f in self.failures if f.table_name == table_name]
            
            if any(f.severity == FailureSeverity.CRITICAL for f in table_failures):
                status_icon = "🔴"
                status_text = f"0 registros | Falha total no carregamento"
            elif table_failures:
                status_icon = "🟡" 
                issues = len([f for f in table_failures if f.severity != FailureSeverity.INFO])
                status_text = f"{metrics.actual_records:,} registros | {issues} problemas"
            else:
                status_icon = "✅"
                status_text = f"{metrics.actual_records:,} registros | 0 falhas"
            
            report += f"├─ {table_name}: {status_icon} {status_text}\n"
        
        # Próximos passos
        if self.failures:
            report += "\n🎯 PRÓXIMOS PASSOS\n"
            critical_actions = [f for f in self.failures if f.severity == FailureSeverity.CRITICAL and f.suggested_action]
            alert_actions = [f for f in self.failures if f.severity == FailureSeverity.ALERT and f.suggested_action]
            
            step = 1
            for failure in critical_actions:
                report += f"{step}. [URGENTE] {failure.suggested_action}\n"
                step += 1
            
            for failure in alert_actions[:3]:  # Máximo 3 alertas
                report += f"{step}. [MÉDIO] {failure.suggested_action}\n"
                step += 1
        
        return report
    
    def get_failure_summary(self) -> Dict[str, Any]:
        """Retorna resumo das falhas para integração"""
        return {
            'total_failures': len(self.failures),
            'critical_count': len([f for f in self.failures if f.severity == FailureSeverity.CRITICAL]),
            'alert_count': len([f for f in self.failures if f.severity == FailureSeverity.ALERT]),
            'tables_with_issues': list(set(f.table_name for f in self.failures)),
            'duration_minutes': (self.end_time - self.start_time).total_seconds() / 60 if self.end_time else 0
        }