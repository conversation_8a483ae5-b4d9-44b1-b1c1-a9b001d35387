"""
ETL Consolidado - Extração de Dados
Todas as extrações de dados consolidadas com rate limiting, retry automático e paginação.
"""

import time
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List
from threading import Lock
from rdstation.crm import CRMClient
from salesforce_integration.config import *
from salesforce_integration.database_connections import get_database_connection
from salesforce_integration.utils import (
    retry_decorator,
    ProgressTracker,
    DataQualityChecker,
    setup_logging
)

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = setup_logging()

# =============================================================================
# CLASSE PRINCIPAL DE EXTRAÇÃO
# =============================================================================

class DataExtractor:
    """Classe principal para extrair dados de todas as fontes"""
    
    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.test_sample = test_sample
        self.extraction_stats = {
            'start_time': None,
            'end_time': None,
            'total_records': 0,
            'sources': {}
        }
        self._rate_limit_lock = Lock()
        self._last_rdstation_request = 0

        if test_sample:
            self.logger.info(f"Modo teste ativado: limitando consultas a {test_sample} registros")
        
    def extract_all_sources(self, sources: List[str] = None) -> Dict[str, pd.DataFrame]:
        """Extrai dados de todas as fontes ou fontes específicas"""
        self.extraction_stats['start_time'] = datetime.now()
        
        if sources is None:
            sources = ['newcon', 'rdstation', 'orbbits', 'quiver']
        
        self.logger.info(f"Iniciando extração de dados das fontes: {', '.join(sources)}")
        
        results = {}
        
        # Extração sequencial para evitar sobrecarga
        for source in sources:
            try:
                self.logger.info(f"Extraindo dados da fonte: {source}")
                
                if source == 'newcon':
                    results.update(self._extract_newcon_all())
                elif source == 'rdstation':
                    results['rdstation_leads'] = self.extract_rdstation_leads()
                elif source == 'orbbits':
                    results.update(self._extract_orbbits_all())
                elif source == 'quiver':
                    results.update(self._extract_quiver_all())
                
                self.logger.info(f"✅ Extração de {source} concluída com sucesso")
                
            except Exception as e:
                self.logger.error(f"❌ Erro na extração de {source}: {e}")
                # Continua com outras fontes em caso de erro
                continue
        
        self.extraction_stats['end_time'] = datetime.now()
        self.extraction_stats['total_records'] = sum(
            len(df) for df in results.values() if isinstance(df, pd.DataFrame)
        )
        
        self._log_extraction_summary(results)
        
        return results

    # Métodos de extração específicos para compatibilidade
    def extract_newcon_clients(self) -> pd.DataFrame:
        """Extrai clientes do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_clients()

    def extract_newcon_leads(self) -> pd.DataFrame:
        """Extrai leads do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_leads()

    def extract_newcon_products(self) -> pd.DataFrame:
        """Extrai produtos do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_products()

    def extract_newcon_proposals(self) -> pd.DataFrame:
        """Extrai propostas do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_proposals()

    def extract_rdstation_leads(self) -> pd.DataFrame:
        """Extrai leads do RD Station"""
        extractor = RDStationExtractor(test_sample=self.test_sample)
        return extractor.extract_leads()

    def extract_orbbits_origin(self) -> pd.DataFrame:
        """Extrai dados de origem do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_origin()

    def extract_orbbits_payments(self) -> pd.DataFrame:
        """Extrai dados de pagamentos do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_payments()

    def extract_orbbits_sales(self) -> pd.DataFrame:
        """Extrai dados de vendas do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_sales()

    def extract_orbbits_prices(self) -> pd.DataFrame:
        """Extrai dados de preços do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_prices()

    def extract_quiver_clients(self) -> pd.DataFrame:
        """Extrai dados de clientes do Quiver"""
        extractor = QuiverExtractor(test_sample=self.test_sample)
        return extractor.extract_clients()

    def extract_quiver_leads(self) -> pd.DataFrame:
        """Extrai dados de leads do Quiver"""
        extractor = QuiverExtractor(test_sample=self.test_sample)
        return extractor.extract_leads()

    def extract_quiver_products(self) -> pd.DataFrame:
        """Extrai dados de produtos do Quiver"""
        extractor = QuiverExtractor(test_sample=self.test_sample)
        return extractor.extract_products()

    def extract_quiver_proposals(self) -> pd.DataFrame:
        """Extrai dados de propostas do Quiver"""
        extractor = QuiverExtractor(test_sample=self.test_sample)
        return extractor.extract_proposals()

    def _extract_newcon_all(self) -> Dict[str, pd.DataFrame]:
        """Extrai todos os dados do NewCon"""
        results = {}
        
        # Extrai cada tabela do NewCon
        extractors = {
            'newcon_clients': self.extract_newcon_clients,
            'newcon_leads': self.extract_newcon_leads,
            'newcon_products': self.extract_newcon_products,
            'newcon_proposals': self.extract_newcon_proposals
        }
        
        for key, extractor in extractors.items():
            try:
                results[key] = extractor()
                self.logger.info(f"✅ {key}: {len(results[key])} registros extraídos")
            except Exception as e:
                self.logger.error(f"❌ Erro em {key}: {e}")
                results[key] = pd.DataFrame()  # DataFrame vazio em caso de erro
        
        return results
    
    def _extract_orbbits_all(self) -> Dict[str, pd.DataFrame]:
        """Extrai todos os dados do Orbbits"""
        results = {}
        
        # Extrai cada tabela do Orbbits
        extractors = {
            'orbbits_origin': self.extract_orbbits_origin,
            'orbbits_payments': self.extract_orbbits_payments,
            'orbbits_sales': self.extract_orbbits_sales,
            'orbbits_prices': self.extract_orbbits_prices
        }
        
        for key, extractor in extractors.items():
            try:
                results[key] = extractor()
                self.logger.info(f"✅ {key}: {len(results[key])} registros extraídos")
            except Exception as e:
                self.logger.error(f"❌ Erro em {key}: {e}")
                results[key] = pd.DataFrame()  # DataFrame vazio em caso de erro
        
        return results

    def _extract_quiver_all(self) -> Dict[str, pd.DataFrame]:
        """Extrai todos os dados do Quiver"""
        results = {}

        # Extrai cada tabela do Quiver
        extractors = {
            'quiver_clients': self.extract_quiver_clients,
            'quiver_leads': self.extract_quiver_leads,
            'quiver_products': self.extract_quiver_products,
            'quiver_proposals': self.extract_quiver_proposals
        }

        for key, extractor in extractors.items():
            try:
                results[key] = extractor()
                self.logger.info(f"✅ {key}: {len(results[key])} registros extraídos")
            except Exception as e:
                self.logger.error(f"❌ Erro em {key}: {e}")
                results[key] = pd.DataFrame()  # DataFrame vazio em caso de erro

        return results

    def _log_extraction_summary(self, results: Dict[str, pd.DataFrame]):
        """Registra resumo da extração"""
        duration = self.extraction_stats['end_time'] - self.extraction_stats['start_time']
        
        self.logger.info("=" * 60)
        self.logger.info("RESUMO DA EXTRAÇÃO")
        self.logger.info("=" * 60)
        self.logger.info(f"Duração total: {duration}")
        self.logger.info(f"Total de registros: {self.extraction_stats['total_records']:,}")
        
        for source, df in results.items():
            if isinstance(df, pd.DataFrame):
                self.logger.info(f"- {source}: {len(df):,} registros")
        
        self.logger.info("=" * 60)

# =============================================================================
# EXTRAÇÃO NEWCON (SQL SERVER)
# =============================================================================

class NewConExtractor:
    """Extrator especializado para NewCon"""

    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.db_name = 'newcon'
        self.test_sample = test_sample

        # Usar SQLLoader para carregar consultas
        from salesforce_integration.sql_loader import sql_loader
        self.sql_loader = sql_loader
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_clients(self) -> pd.DataFrame:
        """Extrai clientes do NewCon usando SQLLoader"""
        self.logger.info("Extraindo clientes do NewCon...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('newcon', 'clientes')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)

                # Processa dados
                if 'id_documento' in df.columns:
                    df['id_documento'] = df['id_documento'].astype(str)

                self.logger.info(f"✅ {len(df)} clientes extraídos do NewCon")
                return df

        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair clientes do NewCon: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_leads(self) -> pd.DataFrame:
        """Extrai leads do NewCon usando SQLLoader"""
        self.logger.info("Extraindo leads do NewCon...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('newcon', 'leads')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)

                # Processa dados
                if 'id_documento' in df.columns:
                    df['id_documento'] = df['id_documento'].astype(str)

                self.logger.info(f"✅ {len(df)} leads extraídos do NewCon")
                return df

        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair leads do NewCon: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_products(self) -> pd.DataFrame:
        """Extrai produtos do NewCon usando SQLLoader"""
        self.logger.info("Extraindo produtos do NewCon...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('newcon', 'produtos')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)

                self.logger.info(f"✅ {len(df)} produtos extraídos do NewCon")
                return df

        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair produtos do NewCon: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_proposals(self) -> pd.DataFrame:
        """Extrai propostas do NewCon usando SQLLoader"""
        self.logger.info("Extraindo propostas do NewCon...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('newcon', 'propostas')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)

                # Processa dados
                if 'id_documento' in df.columns:
                    df['id_documento'] = df['id_documento'].astype(str)

                self.logger.info(f"✅ {len(df)} propostas extraídas do NewCon")
                return df

        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair propostas do NewCon: {e}")
            raise

# =============================================================================
# ORBBITS EXTRACTOR
# =============================================================================

class OrbbitsExtractor:
    """Extrator especializado para Orbbits (MySQL)"""

    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.db_name = 'orbbits'
        self.test_sample = test_sample

        # Usar SQLLoader para carregar consultas
        from salesforce_integration.sql_loader import sql_loader
        self.sql_loader = sql_loader

    @retry_decorator(max_attempts=3, delay=2)
    def extract_origin(self) -> pd.DataFrame:
        """Extrai dados de origem do Orbbits usando SQLLoader"""
        self.logger.info("Extraindo dados de origem do Orbbits...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('orbbits', 'origin')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            self.logger.info("[Orbbits][origin] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][origin] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][origin] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Orbbits][origin] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de origem do Orbbits: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_payments(self) -> pd.DataFrame:
        """Extrai dados de pagamentos do Orbbits usando SQLLoader"""
        self.logger.info("Extraindo dados de pagamentos do Orbbits...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('orbbits', 'payments')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            self.logger.info("[Orbbits][payments] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][payments] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][payments] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Orbbits][payments] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de pagamentos do Orbbits: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_sales(self) -> pd.DataFrame:
        """Extrai dados de vendas do Orbbits usando SQLLoader"""
        self.logger.info("Extraindo dados de vendas do Orbbits...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('orbbits', 'sales')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            self.logger.info("[Orbbits][sales] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][sales] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][sales] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Orbbits][sales] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de vendas do Orbbits: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_prices(self) -> pd.DataFrame:
        """Extrai dados de preços do Orbbits usando SQLLoader"""
        self.logger.info("Extraindo dados de preços do Orbbits...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('orbbits', 'prices')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            self.logger.info("[Orbbits][prices] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][prices] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][prices] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Orbbits][prices] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de preços do Orbbits: {e}")
            raise

# =============================================================================
# RDSTATION EXTRACTOR
# =============================================================================

class RDStationExtractor:
    """Extrator especializado para RD Station"""

    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.test_sample = test_sample

    def extract_leads(self) -> pd.DataFrame:
        """Extrai leads do RD Station"""
        self.logger.info("Extraindo leads do RD Station...")

        # Implementação futura - por enquanto retorna DataFrame vazio
        df = pd.DataFrame()
        self.logger.info("RD Station não implementado ainda - retornando DataFrame vazio")
        return df

# =============================================================================
# QUIVER EXTRACTOR
# =============================================================================

class QuiverExtractor:
    """Extrator especializado para Quiver"""

    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.db_name = 'quiver'
        self.test_sample = test_sample

        # Usar SQLLoader para carregar consultas
        from salesforce_integration.sql_loader import sql_loader
        self.sql_loader = sql_loader

    def _load_sql_query(self, table_name: str) -> str:
        """Carrega consulta SQL usando SQLLoader"""
        try:
            # Carregar consulta usando SQLLoader
            query = self.sql_loader.get_query('quiver', table_name)

            # Adicionar LIMIT se test_sample estiver definido
            if self.test_sample:
                query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

            return query
        except Exception as e:
            self.logger.error(f"Erro ao carregar consulta quiver_{table_name}: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_clients(self) -> pd.DataFrame:
        """Extrai dados de clientes do Quiver"""
        self.logger.info("Extraindo dados de clientes do Quiver...")

        query = self._load_sql_query('clientes')

        try:
            self.logger.info("[Quiver][clients] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Quiver][clients] Conexão aberta com sucesso.")
                self.logger.info("[Quiver][clients] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Quiver][clients] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de clientes do Quiver: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_leads(self) -> pd.DataFrame:
        """Extrai dados de leads do Quiver"""
        self.logger.info("Extraindo dados de leads do Quiver...")

        query = self._load_sql_query('leads')

        try:
            self.logger.info("[Quiver][leads] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Quiver][leads] Conexão aberta com sucesso.")
                self.logger.info("[Quiver][leads] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Quiver][leads] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de leads do Quiver: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_products(self) -> pd.DataFrame:
        """Extrai dados de produtos do Quiver"""
        self.logger.info("Extraindo dados de produtos do Quiver...")

        query = self._load_sql_query('produtos')

        try:
            self.logger.info("[Quiver][products] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Quiver][products] Conexão aberta com sucesso.")
                self.logger.info("[Quiver][products] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Quiver][products] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de produtos do Quiver: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_proposals(self) -> pd.DataFrame:
        """Extrai dados de propostas do Quiver"""
        self.logger.info("Extraindo dados de propostas do Quiver...")

        query = self._load_sql_query('propostas')

        try:
            self.logger.info("[Quiver][proposals] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Quiver][proposals] Conexão aberta com sucesso.")
                self.logger.info("[Quiver][proposals] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Quiver][proposals] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de propostas do Quiver: {e}")
            raise

# =============================================================================
# FUNÇÕES PRINCIPAIS DE EXTRAÇÃO
# =============================================================================

def extract_newcon_clients(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair clientes do NewCon"""
    extractor = NewConExtractor(test_sample)
    return extractor.extract_clients()

def extract_newcon_leads(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair leads do NewCon"""
    extractor = NewConExtractor(test_sample)
    return extractor.extract_leads()

def extract_newcon_products(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair produtos do NewCon"""
    extractor = NewConExtractor(test_sample)
    return extractor.extract_products()

def extract_newcon_proposals(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair propostas do NewCon"""
    extractor = NewConExtractor(test_sample)
    return extractor.extract_proposals()

def extract_orbbits_origin(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair origem do Orbbits"""
    extractor = OrbbitsExtractor(test_sample)
    return extractor.extract_origin()

def extract_orbbits_payments(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair pagamentos do Orbbits"""
    extractor = OrbbitsExtractor(test_sample)
    return extractor.extract_payments()

def extract_orbbits_sales(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair vendas do Orbbits"""
    extractor = OrbbitsExtractor(test_sample)
    return extractor.extract_sales()

def extract_orbbits_prices(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair preços do Orbbits"""
    extractor = OrbbitsExtractor(test_sample)
    return extractor.extract_prices()

def extract_rdstation_leads(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair leads do RD Station"""
    extractor = RDStationExtractor(test_sample)
    return extractor.extract_leads()

def extract_quiver_clients(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair clientes do Quiver"""
    extractor = QuiverExtractor(test_sample)
    return extractor.extract_clients()

def extract_quiver_leads(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair leads do Quiver"""
    extractor = QuiverExtractor(test_sample)
    return extractor.extract_leads()

def extract_quiver_products(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair produtos do Quiver"""
    extractor = QuiverExtractor(test_sample)
    return extractor.extract_products()

def extract_quiver_proposals(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair propostas do Quiver"""
    extractor = QuiverExtractor(test_sample)
    return extractor.extract_proposals()

if __name__ == "__main__":
    # Teste básico
    print("=== TESTE DOS EXTRATORES ===")

    # Teste NewCon
    try:
        df = extract_newcon_clients(test_sample=5)
        print(f"✅ NewCon Clientes: {len(df)} registros")
    except Exception as e:
        print(f"❌ NewCon Clientes: {e}")

    # Teste Quiver
    try:
        df = extract_quiver_clients(test_sample=5)
        print(f"✅ Quiver Clientes: {len(df)} registros")
    except Exception as e:
        print(f"❌ Quiver Clientes: {e}")

    # Teste Orbbits
    try:
        df = extract_orbbits_origin(test_sample=5)
        print(f"✅ Orbbits Origin: {len(df)} registros")
    except Exception as e:
        print(f"❌ Orbbits Origin: {e}")

    print("=== FIM DOS TESTES ===")
